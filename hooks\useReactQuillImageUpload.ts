"use client";

import { useEffect, useRef } from "react";
import { useFetch } from "@/hooks/useFetch";
import API_ROUTE from "@/configs/api";

/**
 * Custom hook for handling image uploads in ReactQuill editor
 * Provides the upload functionality that integrates with the S3 image upload API
 */
export const useReactQuillImageUpload = () => {
	// Image upload API hook
	const {
		data: uploadImageResult,
		error: uploadImageError,
		loading: uploadingImage,
		fetch: uploadImage,
	} = useFetch(API_ROUTE.S3.UPLOAD_IMAGE, {
		method: "POST",
		skip: true,
		options: {
			removeContentType: true,
		},
	});

	// Refs to store the latest upload results for promise resolution
	const uploadImageResultRef = useRef(uploadImageResult);
	const uploadImageErrorRef = useRef(uploadImageError);

	// Update refs when upload results change
	useEffect(() => {
		if (uploadImageResult) {
			uploadImageResultRef.current = uploadImageResult;
		}

		if (uploadImageError) {
			uploadImageErrorRef.current = uploadImageError;
		}
	}, [uploadImageResult, uploadImageError]);

	/**
	 * Handle image upload for ReactQuill editor
	 * @param file - The image file to upload
	 * @returns Promise that resolves to the image URL
	 */
	const handleUploadImage = async (file: File): Promise<string> => {
		const formData = new FormData();
		formData.append("image", file);
		await uploadImage({ body: formData });
		
		return new Promise((resolve, reject) => {
			let retry = 20;
			const checkResult = () => {
				const result = uploadImageResultRef.current;
				const error = uploadImageErrorRef.current;

				console.log("result", result);
				console.log("error", error);
				
				if (!error && result && result.results && result.results.imageKey) {
					resolve(process.env.NEXT_PUBLIC_BASE_API_URL + API_ROUTE.S3.GET_IMAGE(result.results.imageKey));
				} else if (error) {
					reject(error);
				} else if (retry > 0) {
					retry--;
					setTimeout(checkResult, 250);
				} else {
					reject(new Error("Image upload timed out."));
				}
			};
			checkResult();
		}).finally(() => {
			uploadImageResultRef.current = null;
		});
	};

	return {
		handleUploadImage,
		uploadingImage,
		uploadImageError,
	};
};
