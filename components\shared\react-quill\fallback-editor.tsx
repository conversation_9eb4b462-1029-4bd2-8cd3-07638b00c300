"use client";

import { forwardRef } from "react";
import { Textarea } from "@heroui/react";

interface FallbackEditorProps {
	value?: string;
	onChange?: (value: string) => void;
	placeholder?: string;
	className?: string;
}

/**
 * Fallback editor component when ReactQuill fails to load
 * Provides basic text editing functionality
 */
const FallbackEditor = forwardRef<HTMLTextAreaElement, FallbackEditorProps>(({
	value = "",
	onChange,
	placeholder = "Enter your content here...",
	className,
	...props
}, ref) => {
	return (
		<div className="w-full">
			<div className="mb-2 p-2 bg-warning-50 border border-warning-200 rounded-lg">
				<div className="text-warning-700 text-sm">
					⚠️ Rich text editor is unavailable. Using fallback text editor.
				</div>
			</div>
			<Textarea
				ref={ref}
				value={value}
				onValueChange={onChange}
				placeholder={placeholder}
				className={className}
				variant="bordered"
				minRows={10}
				maxRows={20}
				{...props}
			/>
		</div>
	);
});

FallbackEditor.displayName = "FallbackEditor";

export default FallbackEditor;
