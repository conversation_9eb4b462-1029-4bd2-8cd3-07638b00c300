/**
 * Safe Range Manager for Quill Editor
 * Prevents "addRange(): The given range isn't in document" errors
 */

export class SafeRangeManager {
	private quill: any;
	private savedSelection: any = null;
	private isRestoring: boolean = false;
	private isInitialized: boolean = false;

	constructor(quill: any) {
		if (!quill) {
			throw new Error("SafeRangeManager requires a valid Quill instance");
		}
		this.quill = quill;
		this.isInitialized = this.validateQuillInstance();

		if (this.isInitialized) {
			console.log("SafeRangeManager successfully initialized");
		} else {
			console.warn("SafeRangeManager initialized with invalid Quill instance");
		}
	}

	/**
	 * Validate that the Quill instance is properly initialized
	 */
	private validateQuillInstance(): boolean {
		try {
			return !!(
				this.quill &&
				typeof this.quill.getLength === 'function' &&
				typeof this.quill.getSelection === 'function' &&
				typeof this.quill.setSelection === 'function' &&
				this.quill.root &&
				this.quill.root.isConnected
			);
		} catch (error) {
			console.warn("Quill instance validation failed:", error);
			return false;
		}
	}

	/**
	 * Safely save the current selection
	 */
	saveSelection(): void {
		if (!this.isInitialized || !this.isEditorValid()) {
			console.warn("Cannot save selection: editor not ready");
			return;
		}

		try {
			this.savedSelection = this.quill.getSelection();
			console.log("Selection saved:", this.savedSelection);
		} catch (error) {
			console.warn("Could not save selection:", error);
			this.savedSelection = null;
		}
	}

	/**
	 * Safely clear the current selection
	 */
	clearSelection(): void {
		if (!this.isInitialized || !this.isEditorValid()) {
			console.warn("Cannot clear selection: editor not ready");
			return;
		}

		try {
			this.quill.setSelection(null);
			console.log("Selection cleared");
		} catch (error) {
			console.warn("Could not clear selection:", error);
		}
	}

	/**
	 * Safely restore the saved selection
	 */
	restoreSelection(): void {
		if (!this.isInitialized) {
			console.warn("Cannot restore selection: SafeRangeManager not initialized");
			return;
		}

		if (this.isRestoring) {
			console.warn("Already restoring selection, skipping");
			return;
		}

		if (!this.isEditorValid()) {
			console.warn("Cannot restore selection: editor not valid");
			return;
		}

		this.isRestoring = true;

		try {

			const currentLength = this.quill.getLength();
			let targetIndex = 0;

			if (this.savedSelection && this.savedSelection.index !== undefined) {
				// Ensure the saved selection is within bounds
				targetIndex = Math.min(
					Math.max(0, this.savedSelection.index),
					Math.max(0, currentLength - 1)
				);
			} else {
				// Default to end of document
				targetIndex = Math.max(0, currentLength - 1);
			}

			// Validate the target index
			if (targetIndex >= 0 && targetIndex < currentLength) {
				// Use setTimeout to ensure DOM is stable
				setTimeout(() => {
					try {
						if (this.quill && this.quill.getLength && this.quill.root && this.quill.root.isConnected) {
							this.quill.setSelection(targetIndex, 0);
							console.log("Selection restored to index:", targetIndex);
						}
					} catch (delayedError) {
						console.warn("Delayed selection restoration failed:", delayedError);
					} finally {
						this.isRestoring = false;
					}
				}, 100);
			} else {
				console.warn("Invalid target index for selection:", targetIndex, "length:", currentLength);
				this.isRestoring = false;
			}
		} catch (error) {
			console.warn("Could not restore selection:", error);
			this.isRestoring = false;
		}
	}

	/**
	 * Safely focus the editor
	 */
	focus(): void {
		if (!this.isInitialized || !this.isEditorValid()) {
			console.warn("Cannot focus: editor not ready");
			return;
		}

		try {
			// Use setTimeout to ensure the editor is ready
			setTimeout(() => {
				try {
					if (this.isEditorValid() && this.quill.focus) {
						this.quill.focus();
						console.log("Editor focused");
					}
				} catch (focusError) {
					console.warn("Delayed focus failed:", focusError);
				}
			}, 50);
		} catch (error) {
			console.warn("Could not focus editor:", error);
		}
	}

	/**
	 * Check if the editor is in a valid state
	 */
	isEditorValid(): boolean {
		if (!this.isInitialized) {
			return false;
		}

		try {
			return !!(
				this.quill &&
				this.quill.root &&
				this.quill.root.isConnected &&
				this.quill.getLength &&
				typeof this.quill.getLength() === 'number' &&
				this.quill.getLength() >= 0
			);
		} catch (error) {
			console.warn("Editor validation failed:", error);
			return false;
		}
	}

	/**
	 * Get a safe selection position
	 */
	getSafeSelectionIndex(): number {
		if (!this.isInitialized || !this.isEditorValid()) {
			return 0;
		}

		try {
			const length = this.quill.getLength();
			const current = this.quill.getSelection();

			if (current && current.index !== undefined) {
				return Math.min(Math.max(0, current.index), Math.max(0, length - 1));
			}

			return Math.max(0, length - 1);
		} catch (error) {
			console.warn("Could not get safe selection index:", error);
			return 0;
		}
	}

	/**
	 * Reset the manager state
	 */
	reset(): void {
		this.savedSelection = null;
		this.isRestoring = false;
		this.isInitialized = false;
		console.log("SafeRangeManager reset");
	}

	/**
	 * Check if the manager is properly initialized
	 */
	isReady(): boolean {
		return this.isInitialized && this.isEditorValid();
	}
}
