/**
 * Safe Range Manager for Quill Editor
 * Prevents "addRange(): The given range isn't in document" errors
 */

export class SafeRangeManager {
	private quill: any;
	private savedSelection: any = null;
	private isRestoring: boolean = false;

	constructor(quill: any) {
		this.quill = quill;
	}

	/**
	 * Safely save the current selection
	 */
	saveSelection(): void {
		try {
			if (this.quill && this.quill.getSelection) {
				this.savedSelection = this.quill.getSelection();
				console.log("Selection saved:", this.savedSelection);
			}
		} catch (error) {
			console.warn("Could not save selection:", error);
			this.savedSelection = null;
		}
	}

	/**
	 * Safely clear the current selection
	 */
	clearSelection(): void {
		try {
			if (this.quill && this.quill.setSelection) {
				this.quill.setSelection(null);
				console.log("Selection cleared");
			}
		} catch (error) {
			console.warn("Could not clear selection:", error);
		}
	}

	/**
	 * Safely restore the saved selection
	 */
	restoreSelection(): void {
		if (this.isRestoring) {
			console.warn("Already restoring selection, skipping");
			return;
		}

		this.isRestoring = true;

		try {
			if (!this.quill || !this.quill.getLength || !this.quill.setSelection) {
				console.warn("Quill not available for selection restoration");
				return;
			}

			// Check if the editor is still mounted in the DOM
			if (!this.quill.root || !this.quill.root.isConnected) {
				console.warn("Quill editor not connected to DOM");
				return;
			}

			const currentLength = this.quill.getLength();
			let targetIndex = 0;

			if (this.savedSelection && this.savedSelection.index !== undefined) {
				// Ensure the saved selection is within bounds
				targetIndex = Math.min(
					Math.max(0, this.savedSelection.index),
					Math.max(0, currentLength - 1)
				);
			} else {
				// Default to end of document
				targetIndex = Math.max(0, currentLength - 1);
			}

			// Validate the target index
			if (targetIndex >= 0 && targetIndex < currentLength) {
				// Use setTimeout to ensure DOM is stable
				setTimeout(() => {
					try {
						if (this.quill && this.quill.getLength && this.quill.root && this.quill.root.isConnected) {
							this.quill.setSelection(targetIndex, 0);
							console.log("Selection restored to index:", targetIndex);
						}
					} catch (delayedError) {
						console.warn("Delayed selection restoration failed:", delayedError);
					} finally {
						this.isRestoring = false;
					}
				}, 100);
			} else {
				console.warn("Invalid target index for selection:", targetIndex, "length:", currentLength);
				this.isRestoring = false;
			}
		} catch (error) {
			console.warn("Could not restore selection:", error);
			this.isRestoring = false;
		}
	}

	/**
	 * Safely focus the editor
	 */
	focus(): void {
		try {
			if (this.quill && this.quill.focus) {
				// Use setTimeout to ensure the editor is ready
				setTimeout(() => {
					try {
						if (this.quill && this.quill.focus) {
							this.quill.focus();
							console.log("Editor focused");
						}
					} catch (focusError) {
						console.warn("Delayed focus failed:", focusError);
					}
				}, 50);
			}
		} catch (error) {
			console.warn("Could not focus editor:", error);
		}
	}

	/**
	 * Check if the editor is in a valid state
	 */
	isEditorValid(): boolean {
		try {
			return !!(
				this.quill &&
				this.quill.root &&
				this.quill.root.isConnected &&
				this.quill.getLength &&
				typeof this.quill.getLength() === 'number'
			);
		} catch (error) {
			console.warn("Editor validation failed:", error);
			return false;
		}
	}

	/**
	 * Get a safe selection position
	 */
	getSafeSelectionIndex(): number {
		try {
			if (!this.isEditorValid()) {
				return 0;
			}

			const length = this.quill.getLength();
			const current = this.quill.getSelection();

			if (current && current.index !== undefined) {
				return Math.min(Math.max(0, current.index), Math.max(0, length - 1));
			}

			return Math.max(0, length - 1);
		} catch (error) {
			console.warn("Could not get safe selection index:", error);
			return 0;
		}
	}

	/**
	 * Reset the manager state
	 */
	reset(): void {
		this.savedSelection = null;
		this.isRestoring = false;
	}
}
