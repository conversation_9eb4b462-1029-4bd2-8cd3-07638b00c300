"use client";

import "react-quill-new/dist/quill.snow.css";
import "@writergate/quill-image-uploader-nextjs/dist/quill.imageUploader.min.css";
import { ComponentType, forwardRef } from "react";
import dynamic from "next/dynamic";

// TypeScript interface for ReactQuill props
export interface ReactQuillProps {
	modules?: any;
	formats?: string[];
	value?: string;
	onChange?: (value: string) => void;
	className?: string;
	placeholder?: string;
	theme?: string;
	forwardedRef?: any;
}

// Loading component for ReactQuill
const ReactQuillLoading = () => (
	<div className="h-[200px] border border-default-200 rounded-lg flex items-center justify-center">
		<div className="text-default-500">Loading editor...</div>
	</div>
);

// Dynamic ReactQuill component with ImageUploader
const ReactQuill = dynamic(
	async () => {
		try {
			// Import ReactQuill and Quill
			const { default: RQ, Quill } = await import("react-quill-new");

			// Import ImageUploader
			const ImageUploaderModule = await import("@writergate/quill-image-uploader-nextjs");
			const ImageUploader = ImageUploaderModule.default;

			// Register the image uploader module safely
			if (Quill && ImageUploader) {
				Quill.register("modules/imageUploader", ImageUploader);
			}

			// Return the wrapped component with error handling
			const WrappedReactQuill = ({ forwardedRef, ...props }: ReactQuillProps) => {
				// Add error handling for Quill operations
				const handleQuillRef = (quillInstance: any) => {
					if (quillInstance && forwardedRef) {
						// Wrap Quill's setSelection method to handle range errors
						const originalSetSelection = quillInstance.setSelection;
						if (originalSetSelection) {
							quillInstance.setSelection = function(index: any, length?: any, source?: any) {
								try {
									// Validate parameters before calling original method
									if (index === null || index === undefined) {
										return originalSetSelection.call(this, null);
									}

									const currentLength = this.getLength ? this.getLength() : 0;
									if (typeof index === 'number') {
										// Ensure index is within bounds
										const safeIndex = Math.min(Math.max(0, index), Math.max(0, currentLength - 1));
										const safeLength = length !== undefined ? Math.min(length, currentLength - safeIndex) : 0;
										return originalSetSelection.call(this, safeIndex, safeLength, source);
									}

									return originalSetSelection.call(this, index, length, source);
								} catch (error) {
									console.warn("Quill setSelection error caught and handled:", error);
									// Don't throw, just log the error
									return null;
								}
							};
						}

						if (typeof forwardedRef === 'function') {
							forwardedRef(quillInstance);
						} else if (forwardedRef) {
							forwardedRef.current = quillInstance;
						}
					}
				};

				return (
					<RQ
						ref={handleQuillRef}
						{...props}
						preserveWhitespace={true}
						bounds="self"
					/>
				);
			};

			return WrappedReactQuill;
		} catch (error) {
			console.error("Failed to load ReactQuill:", error);
			// Return a fallback component
			return () => (
				<div className="h-[200px] border border-danger-200 rounded-lg flex items-center justify-center">
					<div className="text-danger-500">Failed to load editor. Please refresh the page.</div>
				</div>
			);
		}
	},
	{
		ssr: false,
		loading: ReactQuillLoading,
	}
) as ComponentType<ReactQuillProps>;

export default ReactQuill;
