"use client";

import "react-quill-new/dist/quill.snow.css";
import "@writergate/quill-image-uploader-nextjs/dist/quill.imageUploader.min.css";
import { ComponentType, forwardRef } from "react";
import dynamic from "next/dynamic";

// TypeScript interface for ReactQuill props
export interface ReactQuillProps {
	modules?: any;
	formats?: string[];
	value?: string;
	onChange?: (value: string) => void;
	className?: string;
	placeholder?: string;
	theme?: string;
	forwardedRef?: any;
}

// Loading component for ReactQuill
const ReactQuillLoading = () => (
	<div className="h-[200px] border border-default-200 rounded-lg flex items-center justify-center">
		<div className="text-default-500">Loading editor...</div>
	</div>
);

// Dynamic ReactQuill component with ImageUploader
const ReactQuill = dynamic(
	async () => {
		const ImageUploader = require("@writergate/quill-image-uploader-nextjs").default;
		const { default: RQ, Quill } = await import("react-quill-new");

		// Register the image uploader module
		Quill.register("modules/imageUploader", ImageUploader);

		// Return the wrapped component
		return ({ forwardedRef, ...props }: ReactQuillProps) => {
			return (
				<RQ
					ref={forwardedRef}
					{...props}
					preserveWhitespace={true}
					bounds="self"
				/>
			);
		};
	},
	{
		ssr: false,
		loading: ReactQuillLoading,
	}
) as ComponentType<ReactQuillProps>;

export default ReactQuill;
