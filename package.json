{"name": "portfolio-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroui/react": "^2.7.11", "@writergate/quill-image-uploader-nextjs": "^0.2.8", "cookies-next": "^6.0.0", "framer-motion": "^12.19.1", "moment": "^2.30.1", "next": "15.0.4", "next-themes": "^0.4.6", "quill-delta": "^5.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-quill-new": "^3.4.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.0.4", "eslint-plugin-prettier": "^5.5.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}