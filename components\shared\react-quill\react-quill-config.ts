// ReactQuill configuration constants and utilities

// Supported formats for ReactQuill
export const REACT_QUILL_FORMATS = [
	"header",
	"bold",
	"italic",
	"underline",
	"strike",
	"color",
	"background",
	"list",
	"indent",
	"blockquote",
	"code-block",
	"link",
	"image",
	"video",
	"align",
	"imageBlot",
];

// Default toolbar configuration
export const DEFAULT_TOOLBAR_CONFIG = [
	[{ header: [1, 2, 3, false] }],
	["bold", "italic", "underline", "strike"],
	[{ color: [] }, { background: [] }],
	[{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
	["blockquote", "code-block"],
	["link", "image", "video"],
	[{ align: [] }],
	["clean"], // remove formatting button
];

// Create ReactQuill modules configuration
export const createReactQuillModules = (uploadImageHandler?: (file: File) => Promise<string>) => ({
	toolbar: {
		container: DEFAULT_TOOLBAR_CONFIG,
	},
	imageUploader: uploadImageHandler ? {
		upload: async (file: File) => {
			try {
				console.log("Starting image upload for file:", file.name);

				// Add a small delay to ensure the editor is in a stable state
				await new Promise(resolve => setTimeout(resolve, 50));

				const url = await uploadImageHandler(file);
				console.log("Image upload successful, URL:", url);

				// Add another small delay after upload
				await new Promise(resolve => setTimeout(resolve, 50));

				return url;
			} catch (error) {
				console.error("Image upload failed:", error);
				// Re-throw with more context
				throw new Error(`Failed to upload image "${file.name}": ${error}`);
			}
		},
		newComment: () => Promise.resolve(),
		showComments: () => Promise.resolve([]),
	} : undefined,
	clipboard: {
		// toggle to add extra line breaks when pasting HTML:
		matchVisual: false,
	},
	history: {
		delay: 2000,
		maxStack: 500,
		userOnly: true,
	},
});

// Default ReactQuill props
export const DEFAULT_REACT_QUILL_PROPS = {
	theme: "snow" as const,
	formats: REACT_QUILL_FORMATS,
};
