// ReactQuill configuration constants and utilities

// Supported formats for ReactQuill
export const REACT_QUILL_FORMATS = [
	"header",
	"bold",
	"italic",
	"underline",
	"strike",
	"color",
	"background",
	"list",
	"indent",
	"blockquote",
	"code-block",
	"link",
	"image",
	"video",
	"align",
	"imageBlot",
];

// Default toolbar configuration
export const DEFAULT_TOOLBAR_CONFIG = [
	[{ header: [1, 2, 3, false] }],
	["bold", "italic", "underline", "strike"],
	[{ color: [] }, { background: [] }],
	[{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
	["blockquote", "code-block"],
	["link", "image", "video"],
	[{ align: [] }],
	["clean"], // remove formatting button
];

// Create ReactQuill modules configuration
export const createReactQuillModules = (uploadImageHandler?: (file: File) => Promise<string>) => ({
	toolbar: {
		container: DEFAULT_TOOLBAR_CONFIG,
	},
	imageUploader: uploadImageHandler ? {
		upload: uploadImageHandler,
		newComment: () => Promise.resolve(),
		showComments: () => Promise.resolve([]),
	} : undefined,
	clipboard: {
		// toggle to add extra line breaks when pasting HTML:
		matchVisual: false,
	},
	history: {
		delay: 2000,
		maxStack: 500,
		userOnly: true,
	},
});

// Default ReactQuill props
export const DEFAULT_REACT_QUILL_PROPS = {
	theme: "snow" as const,
	formats: REACT_QUILL_FORMATS,
};
