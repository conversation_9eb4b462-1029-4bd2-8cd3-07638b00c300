"use client";

import { forwardRef, useMem<PERSON>, use<PERSON><PERSON>back, useRef, useImperativeHandle } from "react";
import ReactQuill, { ReactQuillProps } from "./react-quill";
import { useReactQuillImageUpload } from "@/hooks/useReactQuillImageUpload";
import {
	createReactQuillModules,
	DEFAULT_REACT_QUILL_PROPS,
	REACT_QUILL_FORMATS
} from "./react-quill-config";

export interface ReactQuillEditorProps extends Omit<ReactQuillProps, 'modules' | 'formats' | 'theme'> {
	/**
	 * Whether to enable image upload functionality
	 * @default true
	 */
	enableImageUpload?: boolean;
	/**
	 * Custom modules configuration (will be merged with default modules)
	 */
	customModules?: any;
	/**
	 * Custom formats array (defaults to REACT_QUILL_FORMATS)
	 */
	customFormats?: string[];
	/**
	 * Theme for the editor
	 * @default "snow"
	 */
	theme?: string;
}

/**
 * ReactQuill Editor component with integrated image upload functionality
 * This component combines ReactQuill with image upload capabilities and provides
 * a ready-to-use rich text editor for the application.
 */
const ReactQuillEditor = forwardRef<any, ReactQuillEditorProps>(({
	enableImageUpload = true,
	customModules,
	customFormats = REACT_QUILL_FORMATS,
	theme = DEFAULT_REACT_QUILL_PROPS.theme,
	...props
}, ref) => {
	// Internal ref to the ReactQuill instance
	const quillRef = useRef<any>(null);

	// Image upload functionality
	const { handleUploadImage, uploadingImage, uploadImageError } = useReactQuillImageUpload();

	// Enhanced image upload handler that preserves editor state
	const enhancedImageUpload = useCallback(async (file: File): Promise<string> => {
		try {
			console.log("Enhanced image upload starting for:", file.name);

			// Get the current Quill instance
			const quill = quillRef.current?.getEditor?.();
			if (!quill) {
				console.warn("Quill editor not available, falling back to basic upload");
				return await handleUploadImage(file);
			}

			// Save current selection and state
			const selection = quill.getSelection();
			const currentLength = quill.getLength();
			const hasFocus = quill.hasFocus();

			console.log("Editor state before upload:", {
				selection,
				currentLength,
				hasFocus
			});

			// Upload the image
			const imageUrl = await handleUploadImage(file);

			// Safely restore editor state after upload
			try {
				// Ensure the editor is still available
				if (quill && quill.getLength) {
					const newLength = quill.getLength();

					// Restore focus if it was focused before
					if (hasFocus) {
						quill.focus();
					}

					// Set selection to a safe position
					if (selection && selection.index <= newLength) {
						// Use the original selection if it's still valid
						quill.setSelection(selection.index, 0);
					} else {
						// Otherwise, set to the end of the document
						quill.setSelection(newLength - 1, 0);
					}
				}
			} catch (selectionError) {
				console.warn("Could not restore selection after image upload:", selectionError);
				// Don't throw here, the image upload was successful
			}

			console.log("Enhanced image upload completed successfully");
			return imageUrl;
		} catch (error) {
			console.error("Enhanced image upload failed:", error);
			throw error;
		}
	}, [handleUploadImage]);

	// Create modules configuration
	const modules = useMemo(() => {
		const baseModules = createReactQuillModules(
			enableImageUpload ? enhancedImageUpload : undefined
		);

		// Merge with custom modules if provided
		if (customModules) {
			return {
				...baseModules,
				...customModules,
				// Ensure toolbar is properly merged
				toolbar: customModules.toolbar || baseModules.toolbar,
			};
		}

		return baseModules;
	}, [enableImageUpload, enhancedImageUpload, customModules]);

	// Expose the quill instance through the ref
	useImperativeHandle(ref, () => ({
		getEditor: () => quillRef.current?.getEditor?.(),
		focus: () => quillRef.current?.focus?.(),
		blur: () => quillRef.current?.blur?.(),
		...quillRef.current
	}), []);

	return (
		<ReactQuill
			{...props}
			forwardedRef={quillRef}
			modules={modules}
			formats={customFormats}
			theme={theme}
		/>
	);
});

ReactQuillEditor.displayName = "ReactQuillEditor";

export default ReactQuillEditor;
