"use client";

import { forwardRef, useMemo } from "react";
import ReactQuill, { ReactQuillProps } from "./react-quill";
import { useReactQuillImageUpload } from "@/hooks/useReactQuillImageUpload";
import { 
	createReactQuillModules, 
	DEFAULT_REACT_QUILL_PROPS,
	REACT_QUILL_FORMATS 
} from "./react-quill-config";

export interface ReactQuillEditorProps extends Omit<ReactQuillProps, 'modules' | 'formats' | 'theme'> {
	/**
	 * Whether to enable image upload functionality
	 * @default true
	 */
	enableImageUpload?: boolean;
	/**
	 * Custom modules configuration (will be merged with default modules)
	 */
	customModules?: any;
	/**
	 * Custom formats array (defaults to REACT_QUILL_FORMATS)
	 */
	customFormats?: string[];
	/**
	 * Theme for the editor
	 * @default "snow"
	 */
	theme?: string;
}

/**
 * ReactQuill Editor component with integrated image upload functionality
 * This component combines ReactQuill with image upload capabilities and provides
 * a ready-to-use rich text editor for the application.
 */
const ReactQuillEditor = forwardRef<any, ReactQuillEditorProps>(({
	enableImageUpload = true,
	customModules,
	customFormats = REACT_QUILL_FORMATS,
	theme = DEFAULT_REACT_QUILL_PROPS.theme,
	...props
}, ref) => {
	// Image upload functionality
	const { handleUploadImage, uploadingImage, uploadImageError } = useReactQuillImageUpload();

	// Create modules configuration
	const modules = useMemo(() => {
		const baseModules = createReactQuillModules(
			enableImageUpload ? handleUploadImage : undefined
		);
		
		// Merge with custom modules if provided
		if (customModules) {
			return {
				...baseModules,
				...customModules,
				// Ensure toolbar is properly merged
				toolbar: customModules.toolbar || baseModules.toolbar,
			};
		}
		
		return baseModules;
	}, [enableImageUpload, handleUploadImage, customModules]);

	return (
		<ReactQuill
			{...props}
			forwardedRef={ref}
			modules={modules}
			formats={customFormats}
			theme={theme}
		/>
	);
});

ReactQuillEditor.displayName = "ReactQuillEditor";

export default ReactQuillEditor;
