"use client";

import { forwardRef, useMemo, use<PERSON><PERSON>back, useRef, useImperative<PERSON><PERSON>le, useState, useEffect } from "react";
import ReactQuill, { ReactQuillProps } from "./react-quill";
import FallbackEditor from "./fallback-editor";
import ReactQuillErrorBoundary from "./error-boundary";
import { useReactQuillImageUpload } from "@/hooks/useReactQuillImageUpload";
import {
	createReactQuillModules,
	DEFAULT_REACT_QUILL_PROPS,
	REACT_QUILL_FORMATS
} from "./react-quill-config";

export interface ReactQuillEditorProps extends Omit<ReactQuillProps, 'modules' | 'formats' | 'theme'> {
	/**
	 * Whether to enable image upload functionality
	 * @default true
	 */
	enableImageUpload?: boolean;
	/**
	 * Custom modules configuration (will be merged with default modules)
	 */
	customModules?: any;
	/**
	 * Custom formats array (defaults to REACT_QUILL_FORMATS)
	 */
	customFormats?: string[];
	/**
	 * Theme for the editor
	 * @default "snow"
	 */
	theme?: string;
	/**
	 * Whether to use fallback editor if ReactQuill fails to load
	 * @default true
	 */
	useFallback?: boolean;
}

/**
 * ReactQuill Editor component with integrated image upload functionality
 * This component combines ReactQuill with image upload capabilities and provides
 * a ready-to-use rich text editor for the application.
 */
const ReactQuillEditor = forwardRef<any, ReactQuillEditorProps>(({
	enableImageUpload = true,
	customModules,
	customFormats = REACT_QUILL_FORMATS,
	theme = DEFAULT_REACT_QUILL_PROPS.theme,
	useFallback = true,
	...props
}, ref) => {
	// Internal ref to the ReactQuill instance
	const quillRef = useRef<any>(null);

	// Error state for better error handling
	const [hasError, setHasError] = useState(false);
	const [errorMessage, setErrorMessage] = useState<string>("");

	// Image upload functionality
	const { handleUploadImage, uploadingImage, uploadImageError } = useReactQuillImageUpload();

	// Reset error state when component mounts or props change
	useEffect(() => {
		setHasError(false);
		setErrorMessage("");
	}, [enableImageUpload]);

	// Enhanced image upload handler that preserves editor state
	const enhancedImageUpload = useCallback(async (file: File): Promise<string> => {
		try {
			console.log("Enhanced image upload starting for:", file.name);

			// Get the current Quill instance
			const quill = quillRef.current?.getEditor?.();
			if (!quill) {
				console.warn("Quill editor not available, falling back to basic upload");
				return await handleUploadImage(file);
			}

			// Save current selection and state
			const selection = quill.getSelection();
			const currentLength = quill.getLength();
			const hasFocus = quill.hasFocus();

			console.log("Editor state before upload:", {
				selection,
				currentLength,
				hasFocus
			});

			// Upload the image
			const imageUrl = await handleUploadImage(file);

			// Safely restore editor state after upload
			try {
				// Ensure the editor is still available
				if (quill && quill.getLength) {
					const newLength = quill.getLength();

					// Restore focus if it was focused before
					if (hasFocus) {
						quill.focus();
					}

					// Set selection to a safe position
					if (selection && selection.index <= newLength) {
						// Use the original selection if it's still valid
						quill.setSelection(selection.index, 0);
					} else {
						// Otherwise, set to the end of the document
						quill.setSelection(newLength - 1, 0);
					}
				}
			} catch (selectionError) {
				console.warn("Could not restore selection after image upload:", selectionError);
				// Don't throw here, the image upload was successful
			}

			console.log("Enhanced image upload completed successfully");
			return imageUrl;
		} catch (error) {
			console.error("Enhanced image upload failed:", error);
			throw error;
		}
	}, [handleUploadImage]);

	// Create modules configuration
	const modules = useMemo(() => {
		const baseModules = createReactQuillModules(
			enableImageUpload ? enhancedImageUpload : undefined
		);

		// Merge with custom modules if provided
		if (customModules) {
			return {
				...baseModules,
				...customModules,
				// Ensure toolbar is properly merged
				toolbar: customModules.toolbar || baseModules.toolbar,
			};
		}

		return baseModules;
	}, [enableImageUpload, enhancedImageUpload, customModules]);

	// Expose the quill instance through the ref
	useImperativeHandle(ref, () => ({
		getEditor: () => quillRef.current?.getEditor?.(),
		focus: () => quillRef.current?.focus?.(),
		blur: () => quillRef.current?.blur?.(),
		...quillRef.current
	}), []);

	// Error boundary for ReactQuill
	if (hasError) {
		if (useFallback) {
			return (
				<FallbackEditor
					ref={ref}
					value={props.value}
					onChange={props.onChange}
					placeholder={props.placeholder}
					className={props.className}
				/>
			);
		}

		return (
			<div className="h-[200px] border border-danger-200 rounded-lg flex flex-col items-center justify-center p-4">
				<div className="text-danger-500 text-center">
					<div className="font-semibold">Editor Error</div>
					<div className="text-sm mt-1">{errorMessage || "Failed to load the editor"}</div>
					<button
						className="mt-2 px-3 py-1 bg-danger-100 text-danger-700 rounded text-sm hover:bg-danger-200"
						onClick={() => {
							setHasError(false);
							setErrorMessage("");
						}}
					>
						Retry
					</button>
				</div>
			</div>
		);
	}

	try {
		return (
			<ReactQuillErrorBoundary
				useFallbackEditor={useFallback}
				editorProps={{
					value: props.value,
					onChange: props.onChange,
					placeholder: props.placeholder,
					className: props.className,
				}}
			>
				<ReactQuill
					{...props}
					forwardedRef={quillRef}
					modules={modules}
					formats={customFormats}
					theme={theme}
				/>
			</ReactQuillErrorBoundary>
		);
	} catch (error) {
		console.error("ReactQuillEditor render error:", error);
		setHasError(true);
		setErrorMessage(error instanceof Error ? error.message : "Unknown error");
		return null;
	}
});

ReactQuillEditor.displayName = "ReactQuillEditor";

export default ReactQuillEditor;
