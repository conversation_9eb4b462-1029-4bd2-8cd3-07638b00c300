"use client";

import { forwardRef, useMemo, use<PERSON><PERSON>back, useRef, useImperative<PERSON><PERSON>le, useState, useEffect } from "react";
import ReactQuill, { ReactQuillProps } from "./react-quill";
import FallbackEditor from "./fallback-editor";
import ReactQuillErrorBoundary from "./error-boundary";
import { SafeRangeManager } from "./safe-range-manager";
import { useReactQuillImageUpload } from "@/hooks/useReactQuillImageUpload";
import {
	createReactQuillModules,
	DEFAULT_REACT_QUILL_PROPS,
	REACT_QUILL_FORMATS
} from "./react-quill-config";

export interface ReactQuillEditorProps extends Omit<ReactQuillProps, 'modules' | 'formats' | 'theme'> {
	/**
	 * Whether to enable image upload functionality
	 * @default true
	 */
	enableImageUpload?: boolean;
	/**
	 * Custom modules configuration (will be merged with default modules)
	 */
	customModules?: any;
	/**
	 * Custom formats array (defaults to REACT_QUILL_FORMATS)
	 */
	customFormats?: string[];
	/**
	 * Theme for the editor
	 * @default "snow"
	 */
	theme?: string;
	/**
	 * Whether to use fallback editor if ReactQuill fails to load
	 * @default true
	 */
	useFallback?: boolean;
}

/**
 * ReactQuill Editor component with integrated image upload functionality
 * This component combines ReactQuill with image upload capabilities and provides
 * a ready-to-use rich text editor for the application.
 */
const ReactQuillEditor = forwardRef<any, ReactQuillEditorProps>(({
	enableImageUpload = true,
	customModules,
	customFormats = REACT_QUILL_FORMATS,
	theme = DEFAULT_REACT_QUILL_PROPS.theme,
	useFallback = true,
	...props
}, ref) => {
	// Internal ref to the ReactQuill instance
	const quillRef = useRef<any>(null);

	// Safe range manager for handling selections
	const rangeManagerRef = useRef<SafeRangeManager | null>(null);

	// Track editor initialization state
	const [isEditorReady, setIsEditorReady] = useState(false);
	const isInitializedRef = useRef(false);

	// Error state for better error handling
	const [hasError, setHasError] = useState(false);
	const [errorMessage, setErrorMessage] = useState<string>("");

	// Image upload functionality
	const { handleUploadImage, uploadingImage, uploadImageError } = useReactQuillImageUpload();

	// Safe function to get editor instance
	const getEditorSafely = useCallback(() => {
		try {
			const editor = quillRef.current?.getEditor?.();
			if (editor && editor.root && editor.root.isConnected) {
				return editor;
			}
			return null;
		} catch (error) {
			console.warn("Error accessing editor:", error);
			return null;
		}
	}, []);

	// Initialize range manager when quill is ready
	useEffect(() => {
		const initializeEditor = () => {
			const quill = getEditorSafely();
			if (quill && !isInitializedRef.current) {
				try {
					rangeManagerRef.current = new SafeRangeManager(quill);
					setIsEditorReady(true);
					isInitializedRef.current = true;
					console.log("SafeRangeManager initialized");
				} catch (error) {
					console.error("Failed to initialize SafeRangeManager:", error);
					setHasError(true);
					setErrorMessage("Failed to initialize editor");
				}
			}
		};

		// Try to initialize immediately
		initializeEditor();

		// Also try after a short delay in case the editor needs more time
		const timeoutId = setTimeout(initializeEditor, 100);

		return () => {
			clearTimeout(timeoutId);
		};
	}, [getEditorSafely]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (rangeManagerRef.current) {
				rangeManagerRef.current.reset();
				rangeManagerRef.current = null;
			}
			isInitializedRef.current = false;
			setIsEditorReady(false);
		};
	}, []);

	// Reset error state when component mounts or props change
	useEffect(() => {
		setHasError(false);
		setErrorMessage("");
	}, [enableImageUpload]);

	// Enhanced image upload handler using SafeRangeManager
	const enhancedImageUpload = useCallback(async (file: File): Promise<string> => {
		try {
			console.log("Enhanced image upload starting for:", file.name);

			// Check if editor is ready
			if (!isEditorReady || !isInitializedRef.current) {
				console.warn("Editor not ready, falling back to basic upload");
				return await handleUploadImage(file);
			}

			// Get the current Quill instance safely
			const quill = getEditorSafely();
			if (!quill) {
				console.warn("Quill editor not available, falling back to basic upload");
				return await handleUploadImage(file);
			}

			// Ensure range manager is available
			if (!rangeManagerRef.current) {
				console.warn("Range manager not available, falling back to basic upload");
				return await handleUploadImage(file);
			}

			const rangeManager = rangeManagerRef.current;

			// Check if editor is in valid state
			if (!rangeManager.isEditorValid()) {
				console.warn("Editor not in valid state, falling back to basic upload");
				return await handleUploadImage(file);
			}

			// Save current selection using safe range manager
			rangeManager.saveSelection();

			// Clear selection to prevent range issues during upload
			rangeManager.clearSelection();

			// Upload the image
			const imageUrl = await handleUploadImage(file);

			// Wait for DOM to stabilize
			await new Promise(resolve => setTimeout(resolve, 150));

			// Safely restore editor state using range manager
			if (rangeManager.isEditorValid()) {
				rangeManager.focus();
				rangeManager.restoreSelection();
			}

			console.log("Enhanced image upload completed successfully");
			return imageUrl;
		} catch (error) {
			console.error("Enhanced image upload failed:", error);

			// Reset range manager on error
			if (rangeManagerRef.current) {
				rangeManagerRef.current.reset();
			}

			throw error;
		}
	}, [handleUploadImage, isEditorReady, getEditorSafely]);

	// Create modules configuration
	const modules = useMemo(() => {
		const baseModules = createReactQuillModules(
			enableImageUpload ? enhancedImageUpload : undefined
		);

		// Merge with custom modules if provided
		if (customModules) {
			return {
				...baseModules,
				...customModules,
				// Ensure toolbar is properly merged
				toolbar: customModules.toolbar || baseModules.toolbar,
			};
		}

		return baseModules;
	}, [enableImageUpload, enhancedImageUpload, customModules]);

	// Expose the quill instance and range manager through the ref
	useImperativeHandle(ref, () => ({
		getEditor: () => {
			if (!isEditorReady || !isInitializedRef.current) {
				console.warn("Editor not ready");
				return null;
			}
			return getEditorSafely();
		},
		focus: () => {
			if (!isEditorReady || !isInitializedRef.current) {
				console.warn("Cannot focus: editor not ready");
				return;
			}
			if (rangeManagerRef.current) {
				rangeManagerRef.current.focus();
			} else {
				try {
					quillRef.current?.focus?.();
				} catch (error) {
					console.warn("Focus failed:", error);
				}
			}
		},
		blur: () => {
			try {
				quillRef.current?.blur?.();
			} catch (error) {
				console.warn("Blur failed:", error);
			}
		},
		getRangeManager: () => rangeManagerRef.current,
		isReady: () => isEditorReady && isInitializedRef.current,
		...quillRef.current
	}), [isEditorReady, getEditorSafely]);

	// Error boundary for ReactQuill
	if (hasError) {
		if (useFallback) {
			return (
				<FallbackEditor
					ref={ref}
					value={props.value}
					onChange={props.onChange}
					placeholder={props.placeholder}
					className={props.className}
				/>
			);
		}

		return (
			<div className="h-[200px] border border-danger-200 rounded-lg flex flex-col items-center justify-center p-4">
				<div className="text-danger-500 text-center">
					<div className="font-semibold">Editor Error</div>
					<div className="text-sm mt-1">{errorMessage || "Failed to load the editor"}</div>
					<button
						className="mt-2 px-3 py-1 bg-danger-100 text-danger-700 rounded text-sm hover:bg-danger-200"
						onClick={() => {
							setHasError(false);
							setErrorMessage("");
						}}
					>
						Retry
					</button>
				</div>
			</div>
		);
	}

	// Handle editor ready callback
	const handleEditorReady = useCallback((editor: any) => {
		try {
			if (editor && !isInitializedRef.current) {
				rangeManagerRef.current = new SafeRangeManager(editor);
				setIsEditorReady(true);
				isInitializedRef.current = true;
				console.log("Editor ready and SafeRangeManager initialized via callback");
			}
		} catch (error) {
			console.error("Failed to initialize editor via callback:", error);
			setHasError(true);
			setErrorMessage("Failed to initialize editor");
		}
	}, []);

	try {
		return (
			<ReactQuillErrorBoundary
				useFallbackEditor={useFallback}
				editorProps={{
					value: props.value,
					onChange: props.onChange,
					placeholder: props.placeholder,
					className: props.className,
				}}
			>
				<ReactQuill
					{...props}
					forwardedRef={quillRef}
					modules={modules}
					formats={customFormats}
					theme={theme}
					onReady={handleEditorReady}
				/>
			</ReactQuillErrorBoundary>
		);
	} catch (error) {
		console.error("ReactQuillEditor render error:", error);
		setHasError(true);
		setErrorMessage(error instanceof Error ? error.message : "Unknown error");
		return null;
	}
});

ReactQuillEditor.displayName = "ReactQuillEditor";

export default ReactQuillEditor;
