"use client";

import React, { Component, ReactNode } from "react";
import FallbackEditor from "./fallback-editor";

interface ErrorBoundaryProps {
	children: ReactNode;
	fallback?: ReactNode;
	useFallbackEditor?: boolean;
	editorProps?: {
		value?: string;
		onChange?: (value: string) => void;
		placeholder?: string;
		className?: string;
	};
}

interface ErrorBoundaryState {
	hasError: boolean;
	error?: Error;
}

/**
 * Error boundary specifically for ReactQuill components
 * Provides fallback UI when ReactQuill fails to render
 */
class ReactQuillErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): ErrorBoundaryState {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("ReactQuill Error Boundary caught an error:", error, errorInfo);
	}

	render() {
		if (this.state.hasError) {
			// If we have a custom fallback, use it
			if (this.props.fallback) {
				return this.props.fallback;
			}

			// If we should use the fallback editor, render it
			if (this.props.useFallbackEditor && this.props.editorProps) {
				return (
					<FallbackEditor
						value={this.props.editorProps.value}
						onChange={this.props.editorProps.onChange}
						placeholder={this.props.editorProps.placeholder}
						className={this.props.editorProps.className}
					/>
				);
			}

			// Default error UI
			return (
				<div className="h-[200px] border border-danger-200 rounded-lg flex flex-col items-center justify-center p-4">
					<div className="text-danger-500 text-center">
						<div className="font-semibold">Editor Error</div>
						<div className="text-sm mt-1">
							{this.state.error?.message || "Something went wrong with the editor"}
						</div>
						<button 
							className="mt-2 px-3 py-1 bg-danger-100 text-danger-700 rounded text-sm hover:bg-danger-200"
							onClick={() => this.setState({ hasError: false, error: undefined })}
						>
							Retry
						</button>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default ReactQuillErrorBoundary;
